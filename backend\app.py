from flask import Flask, request, jsonify
from flask_cors import CORS
from PIL import Image
import io
import torch
from transformers import AutoImageProcessor, AutoModelForObjectDetection
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 初始化Flask应用
app = Flask(__name__)
CORS(app)

# 加载预训练模型
IMAGE_PROCESSOR = AutoImageProcessor.from_pretrained("facebook/detr-resnet-50")
MODEL = AutoModelForObjectDetection.from_pretrained("facebook/detr-resnet-50")

@app.route('/api/detect', methods=['POST'])
def detect_objects():
    try:
        # 获取上传的图像和检测提示
        file = request.files.get('image')
        prompt = request.form.get('prompt', '')

        if not file:
            return jsonify({'error': '未提供图像文件'}), 400

        # 处理图像
        image = Image.open(io.BytesIO(file.read())).convert('RGB')

        # 准备模型输入
        inputs = IMAGE_PROCESSOR(images=image, return_tensors="pt")

        # 运行目标检测
        with torch.no_grad():
            outputs = MODEL(**inputs)

        # 处理检测结果
        target_sizes = torch.tensor([image.size[::-1]])
        results = IMAGE_PROCESSOR.post_process_object_detection(
            outputs,
            threshold=0.5,
            target_sizes=target_sizes
        )[0]

        # 格式化检测结果
        detections = []
        for score, label, box in zip(
            results["scores"], results["labels"], results["boxes"]
        ):
            box = [round(i, 2) for i in box.tolist()]
            detections.append({
                "class": MODEL.config.id2label[label.item()],
                "score": round(score.item(), 3),
                "box": {
                    "xmin": box[0],
                    "ymin": box[1],
                    "xmax": box[2],
                    "ymax": box[3]
                }
            })

        # 简单情境分析
        situation_analysis = generate_situation_analysis(detections, prompt)

        return jsonify({
            "detections": detections,
            "situation_analysis": situation_analysis
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500


def generate_situation_analysis(detections, prompt):
    """生成简单的情境分析结果"""
    if not detections:
        return {"summary": "未检测到任何物体", "details": []}

    # 统计检测到的物体类别
    class_counts = {}
    for det in detections:
        cls = det["class"]
        class_counts[cls] = class_counts.get(cls, 0) + 1

    # 生成分析摘要
    summary = f"检测到{len(detections)}个物体，包括"
    summary += ", ".join([f"{count}个{cls}" for cls, count in class_counts.items()])

    # 如果有用户提示，加入相关分析
    if prompt:
        summary += f"。用户提示: '{prompt}'"

    return {
        "summary": summary,
        "class_distribution": class_counts,
        "details": [
            f"{det['class']} (置信度: {det['score']:.2f})"
            for det in detections
        ]
    }

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)